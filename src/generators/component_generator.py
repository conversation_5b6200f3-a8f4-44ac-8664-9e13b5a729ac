# -*- coding: utf-8 -*-
"""
Component Generator - Geração final de código Angular.

Este módulo é responsável pela geração final de código Angular a partir dos dados
processados do Figma e mapeamentos com o Design System.
"""

import re
from pathlib import Path
from typing import Dict, Any, List, Tuple
from dataclasses import dataclass

from src.utils.logging import get_logger
from src.utils.file_operations import FileOperations
from src.utils.component_analyzer import ComponentAnalyzer
from src.utils.angular_utils import (
    normalize_to_kebab_case, 
    generate_angular_component_files
)
from src.utils.figma_utils import (
    load_design_system_colors,
    extract_structure_info
)
from src.generators.utils.figma_data_processor import FigmaDataProcessor
from src.generators.utils.generator_config_loader import GeneratorConfigLoader
from src.generators.utils.element_order_validator import ElementOrderValidator
from src.generators.figma_reader import FigmaComponentData
from src.generators.design_system_mapper import DesignSystemMapping
from src.utils.color_mapper import create_color_mapper_from_design_system

logger = get_logger(__name__)

@dataclass
class GeneratedComponent:
    """Componente Angular gerado."""
    name: str
    html_template: str
    typescript_code: str
    scss_styles: str
    metadata: Dict[str, Any]

class ComponentGenerator:
    """
    Gera código final (HTML, TS, SCSS) usando IA, prompts centralizados e templates do Design System.
    """
    def __init__(self, config_path: str = "project_config.yaml", flow_client=None):
        # Usar o novo utilitário de configuração
        self.config_helper = GeneratorConfigLoader(config_path)
        self.config = self.config_helper.get_config()
        self.ai_config = self.config_helper.get_ai_config()
        self.prompts = self.config_helper.load_prompts()
        self.flow_client = flow_client

        # Inicializar processador de dados do Figma
        self.figma_processor = FigmaDataProcessor(flow_client=self.flow_client)

        # Inicializar validador de ordem dos elementos
        self.order_validator = ElementOrderValidator()


    def generate_component_code(self, figma_data: FigmaComponentData, mapping: DesignSystemMapping, output_dir: str):
        """
        Gera HTML, TS e SCSS para o componente Angular completo usando fluxo sequencial.
        """
        logger.info(f"\n🚀 Gerando código final para: {figma_data.component_name}")

        # Verificar se é um componente wrapper
        is_wrapper = self._is_wrapper_component(figma_data)
        child_components = self._get_child_components(figma_data)

        if is_wrapper and child_components:
            logger.info(f"📦 Componente wrapper detectado. Componentes filhos: {child_components}")
            # Para wrappers, apenas salvar metadados para geração posterior
            self._save_wrapper_metadata(figma_data, child_components, output_dir)
            return

        # Pré-processar dados do Figma com IA
        ai_processed_data = self.figma_processor.preprocess_with_ai(figma_data)

        # Preparar contexto base para IA
        base_context = self._prepare_complete_context(figma_data, mapping, ai_processed_data)

        # FLUXO SEQUENCIAL CONSOLIDADO
        from .utils.validation_engine import ValidationEngine
        from .utils.ai_analysis_helper import AIAnalysisHelper

        # Inicializar utilitários de validação
        validation_engine = ValidationEngine()
        ai_helper = AIAnalysisHelper(self.flow_client, self.prompts, self.ai_config)

        # Gerar código em sequência usando métodos consolidados
        html, typescript, scss = self.generate_with_context_flow(base_context)

        # Validação e correção automática
        html, typescript, scss = validation_engine.validate_and_fix_all(html, typescript, scss)

        # Validação final com IA
        html, typescript, scss = ai_helper.final_validation_with_ai(html, typescript, scss, base_context)

        # Salvar arquivos do componente Angular
        self._save_component_files(figma_data.normalized_name, html, typescript, scss, output_dir)

    def _save_wrapper_metadata(self, figma_data: FigmaComponentData, child_components: List[str], output_dir: str):
        """
        Salva metadados do wrapper para geração posterior via IA.
        
        Args:
            figma_data: Dados do Figma
            child_components: Lista de componentes filhos
            output_dir: Diretório de saída
        """
        # Normalizar nomes dos componentes filhos
        normalized_children = []
        for child in child_components:
            normalized_children.append(normalize_to_kebab_case(child))
        
        # Criar metadados do wrapper
        wrapper_metadata = {
            'wrapper_name': figma_data.component_name,
            'normalized_name': figma_data.normalized_name,
            'figma_id': figma_data.figma_id,
            'child_components': normalized_children,
            'figma_data': figma_data.__dict__,
            'output_dir': output_dir
        }
        
        # Salvar metadados em arquivo JSON na pasta figma_processed
        figma_processed_dir = output_dir.replace('/angular', '/figma_processed')
        metadata_path = Path(figma_processed_dir) / f"{figma_data.normalized_name}_wrapper_metadata.json"

        # Usar o utilitário de salvamento
        if FileOperations.save_json_file(wrapper_metadata, metadata_path):
            logger.info(f"💾 Metadados do wrapper salvos: {metadata_path}")
        else:
            logger.error(f"❌ Erro ao salvar metadados do wrapper: {metadata_path}")
    
    def _prepare_complete_context(self, figma_data: FigmaComponentData, mapping: DesignSystemMapping, ai_processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepara contexto completo para geração do componente Angular."""
        
        logger.info(f"🔍 Preparando contexto para: {figma_data.component_name}")
        logger.debug(f"   Figma ID: {figma_data.figma_id}")
        logger.debug(f"   Dados processados pela IA: {len(ai_processed_data)} seções")
        
        # Mapear webcomponents para templates do Design System
        mapped_components = []
        loaded_templates = set()  # Para detectar duplicatas
        
        for map_item in mapping.mappings:
            if map_item.design_system_component:
                template_file = map_item.design_system_component.file_path
                
                # Verificar se template já foi carregado
                if template_file in loaded_templates:
                    logger.debug(f"   Template duplicado ignorado: {template_file}")
                    continue  # Pular duplicatas
                
                template = self._load_ds_template(template_file)
                loaded_templates.add(template_file)
                
                # Determinar tipo de webcomponent
                webcomponent_type = map_item.figma_webcomponent.get('type', 'confirmed')
                confidence_note = f" (possible: {map_item.confidence:.2f})" if webcomponent_type == 'possible' else f" (confirmed: {map_item.confidence:.2f})"
                logger.debug(f"   Template {len(loaded_templates)}: {template_file}{confidence_note}")
                
                mapped_components.append({
                    "figma": map_item.figma_webcomponent,
                    "design_system": {
                        "name": map_item.design_system_component.name,
                        "description": map_item.design_system_component.description,
                        "template": template,
                        "category": map_item.design_system_component.category
                    },
                    "confidence": map_item.confidence,
                    "properties_mapping": map_item.properties_mapping,
                    "webcomponent_type": webcomponent_type
                })
        
        # Mapear cores do Figma para classes CSS do Design System
        logger.debug("   Mapeando cores do Figma...")
        color_mapping = self._map_figma_colors_to_css_classes(figma_data)
        logger.debug(f"   Cores mapeadas: {len(color_mapping)} classes")
        
        # Extrair informações de estrutura
        logger.debug("   Extraindo informações de estrutura...")
        structure_info = extract_structure_info(figma_data.__dict__)
        
        # Contexto completo para IA (convertido para dict serializável)
        context = {
            "component_name": figma_data.component_name,
            "figma_id": figma_data.figma_id,
            "raw_html": figma_data.html_structure,
            "ai_processed_data": ai_processed_data,
            "mapped_components": mapped_components,
            "total_components": len(mapped_components),
            "component_type": self._detect_component_type(figma_data),
            "color_mapping": color_mapping,
            "structure_info": structure_info,
            "figma_css_styles": figma_data.css_styles,  # Adicionar estilos CSS do Figma
            "metadata": {
                "figma_file": figma_data.metadata.get('figma_file', ''),
                "component_type": figma_data.metadata.get('component_type', ''),
                "webcomponents_count": figma_data.metadata.get('webcomponents_count', 0),
                "has_children": figma_data.metadata.get('has_children', False),
                "generation_method": figma_data.metadata.get('generation_method', ''),
                "total_webcomponents": figma_data.metadata.get('total_webcomponents', 0)
            }
        }
        
        # Adicionar conteúdo específico do header se existir
        if structure_info.get('header_content'):
            logger.debug("   Adicionando conteúdo do header")
            context['header_content'] = structure_info['header_content']
        
        # Adicionar conteúdo específico das actions se existir
        if structure_info.get('actions_content'):
            logger.debug("   Adicionando conteúdo das actions")
            context['actions_content'] = structure_info['actions_content']
        
        # Log resumo do contexto
        logger.debug("Resumo do contexto:")
        logger.debug(f"   Templates carregados: {len(loaded_templates)}")
        logger.debug(f"   Classes de cores: {len(color_mapping)}")
        logger.debug(f"   Seções de estrutura: {len(structure_info)}")
        logger.debug(f"   HTML do Figma: {len(figma_data.html_structure)} chars")
        logger.debug(f"   Dados processados pela IA: {len(ai_processed_data)} seções")
        
        return context

    def generate_with_context_flow(self, base_context: Dict[str, Any]) -> Tuple[str, str, str]:
        """
        Gera HTML, TypeScript e SCSS em sequência com contexto compartilhado.

        Args:
            base_context: Contexto base com dados do Figma e mapeamento

        Returns:
            Tuple[html, typescript, scss]
        """
        logger.info(f"🔄 Iniciando geração sequencial do componente '{base_context.get('component_name', '')}'")

        # ETAPA 0: Aplicar validações de ordem e duplicatas
        logger.info("🔍 Validando ordem e duplicatas...")
        validated_context = self._apply_order_validations(base_context)

        # ETAPA 1: Gerar HTML
        logger.info("📝 Etapa 1/3: Gerando HTML...")
        html = self._generate_html(validated_context)

        # ETAPA 2: Gerar TypeScript com contexto do HTML
        logger.info("📝 Etapa 2/3: Gerando TypeScript com contexto do HTML...")
        ts_context = self._build_typescript_context(base_context, html)
        typescript = self._generate_typescript(ts_context)

        # ETAPA 3: Gerar SCSS com contexto HTML + TypeScript
        logger.info("📝 Etapa 3/3: Gerando SCSS com contexto completo...")
        scss_context = self._build_scss_context(base_context, html, typescript)
        scss = self._generate_scss(scss_context)

        logger.info("✅ Geração sequencial concluída")
        return html, typescript, scss

    def _generate_html(self, context: Dict[str, Any]) -> str:
        """Gera HTML usando prompt específico."""
        try:
            if not self.flow_client:
                logger.error("❌ Flow API não disponível para geração de HTML.")
                return self._generate_fallback_html(context)

            prompt = self.prompts['generation']['html']['system_prompt']
            user_prompt = self.prompts['generation']['html']['user_prompt']

            # Preparar contexto específico para HTML
            html_context = self._prepare_html_context(context)

            model = self.ai_config['model']['generation']
            temperature = self.ai_config.get('temperature')
            max_tokens = self.ai_config.get('max_tokens')
            # Formatar o user_prompt de forma segura
            try:
                formatted_user_prompt = user_prompt.format(**html_context)
            except (KeyError, ValueError) as e:
                logger.warning(f"⚠️ Erro na formatação do prompt HTML: {e}")
                formatted_user_prompt = user_prompt

            response = self.flow_client.with_model(model).get_answer(
                system_prompt=prompt,
                user_prompt=formatted_user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )

            return self._sanitize_ai_response(response)

        except Exception as e:
            logger.error(f"❌ Erro na geração de HTML: {e}")
            return self._generate_fallback_html(context)

    def _generate_typescript(self, context: Dict[str, Any]) -> str:
        """Gera TypeScript usando prompt específico com contexto do HTML."""
        try:
            if not self.flow_client:
                logger.error("❌ Flow API não disponível para geração de TypeScript.")
                return self._generate_fallback_typescript(context)

            prompt = self.prompts['generation']['typescript']['system_prompt']
            user_prompt = self.prompts['generation']['typescript']['user_prompt']

            model = self.ai_config['model']['generation']
            temperature = self.ai_config.get('temperature')
            max_tokens = self.ai_config.get('max_tokens')

            # Formatar o user_prompt de forma segura
            try:
                formatted_user_prompt = user_prompt.format(**context)
            except (KeyError, ValueError) as e:
                logger.warning(f"⚠️ Erro na formatação do prompt TypeScript: {e}")
                formatted_user_prompt = user_prompt

            response = self.flow_client.with_model(model).get_answer(
                system_prompt=prompt,
                user_prompt=formatted_user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )

            return self._sanitize_ai_response(response)

        except Exception as e:
            logger.error(f"❌ Erro na geração de TypeScript: {e}")
            return self._generate_fallback_typescript(context)

    def _generate_scss(self, context: Dict[str, Any]) -> str:
        """Gera SCSS usando prompt específico com contexto HTML + TypeScript."""
        try:
            if not self.flow_client:
                logger.error("❌ Flow API não disponível para geração de SCSS.")
                return self._generate_fallback_scss(context)

            prompt = self.prompts['generation']['scss']['system_prompt']
            user_prompt = self.prompts['generation']['scss']['user_prompt']

            # Debug: Log do contexto
            # component_name = context.get('component_name', 'unknown')

            model = self.ai_config['model']['generation']
            temperature = self.ai_config.get('temperature')
            max_tokens = self.ai_config.get('max_tokens')

            # Formatar o user_prompt de forma segura
            try:
                formatted_user_prompt = user_prompt.format(**context)
            except (KeyError, ValueError) as e:
                logger.warning(f"⚠️ Erro na formatação do prompt SCSS: {e}")
                # Log das variáveis faltantes
                import string
                formatter = string.Formatter()
                missing_vars = [field_name for _, field_name, _, _ in formatter.parse(user_prompt) if field_name is not None]
                available_vars = list(context.keys())
                missing = [var for var in missing_vars if var not in available_vars]
                logger.warning(f"   Variáveis faltantes: {missing}")
                logger.warning(f"   Variáveis disponíveis: {available_vars}")
                formatted_user_prompt = user_prompt

            response = self.flow_client.with_model(model).get_answer(
                system_prompt=prompt,
                user_prompt=formatted_user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )

            sanitized = self._sanitize_ai_response(response)
            return sanitized

        except Exception as e:
            logger.error(f"❌ Erro na geração de SCSS: {e}")
            return self._generate_fallback_scss(context)

    def _build_typescript_context(self, base_context: Dict[str, Any], html: str) -> Dict[str, Any]:
        """Constrói contexto para TypeScript incluindo análise do HTML."""
        ts_context = base_context.copy()

        # Analisar HTML para extrair informações necessárias para TypeScript
        html_analysis = self._analyze_html_for_typescript(html)

        ts_context.update({
            'generated_html': html,
            'html_analysis': html_analysis,
            'required_methods': html_analysis.get('methods', []),
            'required_properties': html_analysis.get('properties', []),
            'required_imports': html_analysis.get('imports', [])
        })

        return ts_context

    def _build_scss_context(self, base_context: Dict[str, Any], html: str, typescript: str) -> Dict[str, Any]:
        """Constrói contexto para SCSS incluindo análise do HTML e TypeScript."""
        scss_context = base_context.copy()

        # Analisar HTML e TypeScript para SCSS
        html_analysis = self._analyze_html_for_scss(html)
        ts_analysis = self._analyze_typescript_for_scss(typescript)

        scss_context.update({
            'generated_html': html,
            'generated_typescript': typescript,
            'html_analysis': html_analysis,
            'ts_analysis': ts_analysis,
            'css_classes_used': html_analysis.get('classes', []),
            'component_structure': html_analysis.get('structure', {}),
            'design_system_classes': html_analysis.get('design_system_classes', []),
            'component_name': base_context.get('component_name', ''),
            'raw_html': base_context.get('raw_html', ''),
            'ai_processed_data': base_context.get('ai_processed_data', {})
        })

        return scss_context

    def _prepare_html_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Prepara contexto específico para geração de HTML."""
        html_context = context.copy()

        # Extrair templates do design system mapeados
        design_system_templates = []
        for mapped_comp in context.get('mapped_components', []):
            if 'design_system' in mapped_comp and 'template' in mapped_comp['design_system']:
                design_system_templates.append({
                    'name': mapped_comp['design_system']['name'],
                    'template': mapped_comp['design_system']['template'],
                    'confidence': mapped_comp.get('confidence', 0)
                })

        html_context['design_system_templates'] = design_system_templates
        html_context['component_type'] = context.get('component_type', 'component')

        # Adicionar ordem dos botões se disponível (será usado nas regras críticas)
        if context.get('button_order'):
            html_context['button_order'] = context['button_order']
            logger.info(f"📋 Ordem dos botões será aplicada: {context['button_order']}")

        return html_context

    def _analyze_html_for_typescript(self, html: str) -> Dict[str, Any]:
        """Analisa HTML para extrair informações necessárias para TypeScript."""
        analysis = {
            'methods': [],
            'properties': [],
            'imports': [],
            'directives_used': []
        }

        # Extrair métodos de eventos
        method_matches = re.findall(r'\(click\)="(\w+)\(\)"', html)
        analysis['methods'].extend(method_matches)

        # Extrair propriedades de ngModel
        property_matches = re.findall(r'\[\(ngModel\)\]="(\w+)"', html)
        analysis['properties'].extend(property_matches)

        # Detectar diretivas Angular
        if '*ngFor' in html or '*ngIf' in html:
            analysis['imports'].append('CommonModule')
            analysis['directives_used'].append('structural')

        if '[(ngModel)]' in html:
            analysis['imports'].append('FormsModule')
            analysis['directives_used'].append('forms')

        return analysis

    def _analyze_html_for_scss(self, html: str) -> Dict[str, Any]:
        """Analisa HTML para extrair informações necessárias para SCSS."""
        analysis = {
            'classes': [],
            'ids': [],
            'design_system_classes': [],
            'structure': {}
        }

        # Extrair classes CSS
        class_matches = re.findall(r'class="([^"]*)"', html)
        for match in class_matches:
            classes = match.split()
            analysis['classes'].extend(classes)

            # Identificar classes do design system (brad-*)
            ds_classes = [cls for cls in classes if cls.startswith('brad-')]
            analysis['design_system_classes'].extend(ds_classes)

        # Extrair IDs
        id_matches = re.findall(r'id="([^"]*)"', html)
        analysis['ids'].extend(id_matches)

        # Analisar estrutura básica
        analysis['structure'] = {
            'has_form': '<form' in html,
            'has_table': 'brad-table' in html,
            'has_modal': 'brad-modal' in html or 'modal' in html.lower(),
            'has_buttons': 'brad-button' in html or '<button' in html
        }

        return analysis

    def _analyze_typescript_for_scss(self, typescript: str) -> Dict[str, Any]:
        """Analisa TypeScript para extrair informações úteis para SCSS."""
        analysis = {
            'component_name': '',
            'has_inputs': False,
            'has_outputs': False,
            'methods': []
        }

        # Extrair nome do componente
        component_match = re.search(r'export class (\w+)Component', typescript)
        if component_match:
            analysis['component_name'] = component_match.group(1)

        # Detectar @Input e @Output
        analysis['has_inputs'] = '@Input' in typescript
        analysis['has_outputs'] = '@Output' in typescript

        # Extrair métodos
        method_matches = re.findall(r'(\w+)\(\)\s*{', typescript)
        analysis['methods'] = method_matches

        return analysis

    def _apply_order_validations(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Aplica validações de ordem e duplicatas no contexto."""
        try:
            validated_context = context.copy()

            # Validar ordem dos elementos se houver dados processados
            if 'ai_processed_data' in context:
                ai_data = context['ai_processed_data']

                # Validar ordem dos elementos de texto
                if 'text_elements' in ai_data:
                    text_elements = ai_data['text_elements']
                    if text_elements and 'figma_data' in context:
                        ordered_texts = self.order_validator.validate_element_order(
                            context['figma_data'], text_elements
                        )
                        validated_context['ai_processed_data']['text_elements'] = ordered_texts

                # Extrair ordem correta dos botões do Figma
                if 'figma_data' in context:
                    button_order = self.order_validator.extract_button_order_from_figma(
                        context['figma_data']
                    )
                    if button_order:
                        validated_context['button_order'] = button_order
                        logger.info(f"📋 Ordem dos botões definida: {button_order}")

            return validated_context

        except Exception as e:
            logger.error(f"❌ Erro ao aplicar validações de ordem: {e}")
            return context

    def _generate_fallback_html(self, context: Dict[str, Any]) -> str:
        """Gera HTML de fallback quando IA não está disponível."""
        component_name = context['component_name']
        html = f"""<!-- Componente Angular: {component_name} -->
<div class="{component_name}-container">
  <!-- ERRO NA GERAÇÃO, fallback criado -->
  <!-- TODO: Implementar estrutura HTML baseada nos componentes mapeados -->
  <p>Componente {component_name} - Estrutura HTML a ser implementada</p>
</div>"""
        return html

    def _generate_fallback_typescript(self, context: Dict[str, Any]) -> str:
        """Gera TypeScript de fallback quando IA não está disponível."""
        component_name = context['component_name']
        normalized_name = component_name.replace(' ', '-').lower()
        class_name = ''.join(word.capitalize() for word in normalized_name.split('-')) + 'Component'
        
        ts = f"""import {{ Component, Input, Output, EventEmitter }} from '@angular/core';

@Component({{
  selector: 'app-{normalized_name}',
  templateUrl: './{normalized_name}.component.html',
  styleUrls: ['./{normalized_name}.component.scss']
}})
export class {class_name} {{
  // ERRO NA GERAÇÃO, fallback criado
  // TODO: Implementar propriedades e métodos baseados nos componentes mapeados
}}"""
        return ts

    def _generate_fallback_scss(self, context: Dict[str, Any]) -> str:
        """Gera SCSS de fallback quando IA não está disponível."""
        component_name = context['component_name']
        scss = f""".{component_name}-container {{
  // ERRO NA GERAÇÃO, fallback criado
  // TODO: Implementar estilos baseados nos componentes mapeados
}}"""
        return scss

    def _load_ds_template(self, file_path: str) -> str:
        try:
            if not file_path:
                logger.debug("   ⚠️ Caminho de template vazio")
                return ""
            
            # Verificar se arquivo existe
            if not Path(file_path).exists():
                logger.warning(f"   Arquivo não encontrado: {file_path}")
                return ""
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                logger.debug(f"   Carregado: {file_path} ({len(content)} chars)")
                return content
        except Exception as e:
            logger.warning(f"   Erro ao carregar template do Design System: {e}")
            return ""
    
    def _map_figma_colors_to_css_classes(self, figma_data: FigmaComponentData) -> Dict[str, str]:
        """
        Mapeia cores do Figma para classes CSS do Design System.
        
        Args:
            figma_data: Dados do Figma
            
        Returns:
            Dict com mapeamento de cores para classes CSS
        """
        try:
            # Carregar dados do Design System para cores
            design_system_data = load_design_system_colors()
            
            if not design_system_data:
                logger.warning("⚠️ Dados do Design System não encontrados para mapeamento de cores")
                return {}
            
            # Criar mapeador de cores
            color_mapper = create_color_mapper_from_design_system(design_system_data)
            
            # Converter dados do Figma para formato esperado
            figma_dict = {
                'id': figma_data.figma_id,
                'name': figma_data.component_name,
                'css': figma_data.css_styles,
                'children': []
            }
            
            # Adicionar children se disponíveis
            if figma_data.metadata.get('children'):
                figma_dict['children'] = figma_data.metadata.get('children', [])
            
            logger.debug(f"🎨 Dados do Figma para mapeamento de cores: {figma_dict}")
            
            # Mapear cores
            color_mapping = color_mapper.map_figma_colors_to_css_classes(figma_dict)
            
            logger.info(f"🎨 Mapeadas {len(color_mapping)} cores do Figma para classes CSS")
            return color_mapping
            
        except Exception as e:
            logger.error(f"❌ Erro no mapeamento de cores: {e}")
            return {}

    def _detect_component_type(self, figma_data: FigmaComponentData) -> str:
        """Detecta o tipo do componente baseado no nome e estrutura."""
        return ComponentAnalyzer.detect_component_type(figma_data.component_name)

    def _is_wrapper_component(self, figma_data: FigmaComponentData) -> bool:
        """Detecta se o componente é um wrapper (arquivo principal)."""
        all_components = figma_data.metadata.get('all_components_names', [])
        return ComponentAnalyzer.is_wrapper_component(figma_data.component_name, all_components)

    def _get_child_components(self, figma_data: FigmaComponentData) -> List[str]:
        """Obtém a lista de componentes filhos."""
        all_components = figma_data.metadata.get('all_components_names', [])
        return ComponentAnalyzer.get_child_components(figma_data.component_name, all_components)

    def _sanitize_ai_response(self, response: str) -> str:
        """Sanitiza a resposta da IA removendo blocos de código desnecessários."""
        return ComponentAnalyzer.sanitize_ai_response(response)

    def _save_component_files(self, component_name: str, html: str, ts: str, scss: str, output_dir: str):
        """Salva arquivos do componente Angular."""
        # Salvar arquivos usando utilitário Angular
        component_dir = generate_angular_component_files(component_name, html, ts, scss, output_dir)

        # Log de confirmação
        logger.info(f"💾 Componente Angular salvo em {component_dir}")
        logger.debug(f"   HTML: {len(html)} caracteres")
        logger.debug(f"   TypeScript: {len(ts)} caracteres")
        logger.debug(f"   SCSS: {len(scss)} caracteres")
