#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de teste para a nova funcionalidade de geração integrada de wrappers.

Este script demonstra como usar a nova funcionalidade de geração integrada
que gera HTML, TypeScript e SCSS em uma única chamada à IA, seguida de
validação específica para wrappers.
"""

import sys
import os
from pathlib import Path

# Adicionar o diretório raiz ao path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.generators.wrapper_generator import WrapperGenerator
from src.utils.logging import get_logger

logger = get_logger(__name__)

def test_wrapper_integrated_generation():
    """
    Testa a geração integrada de wrappers.
    """
    logger.info("🧪 Iniciando teste da geração integrada de wrappers")
    
    # Configurar gerador de wrapper
    config_path = "project_config.yaml"
    
    # Nota: Para teste real, você precisaria de um flow_client configurado
    # flow_client = configure_flow_client()
    flow_client = None  # Para teste sem IA
    
    wrapper_generator = WrapperGenerator(config_path, flow_client)
    
    # Diretório de exemplo onde procurar metadados
    output_dir = "data/output_generated/sat_contabilidade/modal_vinculo/angular/modal-vinculo"
    
    if not Path(output_dir).exists():
        logger.warning(f"⚠️ Diretório de teste não encontrado: {output_dir}")
        logger.info("📝 Para testar, você precisa:")
        logger.info("   1. Ter metadados de wrapper gerados em algum diretório")
        logger.info("   2. Configurar um flow_client válido")
        logger.info("   3. Executar: wrapper_generator.generate_wrappers_integrated_from_metadata(output_dir)")
        return
    
    try:
        # Usar a nova funcionalidade integrada
        logger.info("🚀 Testando geração integrada de wrappers...")
        wrapper_generator.generate_wrappers_integrated_from_metadata(output_dir)
        
        logger.info("✅ Teste concluído!")
        
    except Exception as e:
        logger.error(f"❌ Erro no teste: {e}")

def compare_old_vs_new_generation():
    """
    Compara a geração antiga (3 chamadas) vs nova (1 chamada integrada).
    """
    logger.info("📊 Comparando geração antiga vs nova")
    
    logger.info("🔄 GERAÇÃO ANTIGA (3 chamadas separadas):")
    logger.info("   1. _generate_wrapper_html_ai(context)")
    logger.info("   2. _generate_wrapper_typescript_ai(context, html)")
    logger.info("   3. _generate_wrapper_scss_ai(context, html)")
    logger.info("   ❌ Sem validação final integrada")
    
    logger.info("🆕 GERAÇÃO NOVA (1 chamada integrada):")
    logger.info("   1. _generate_wrapper_integrated_ai(context) -> html, ts, scss")
    logger.info("   2. ai_helper.validate_wrapper_with_ai(html, ts, scss, context)")
    logger.info("   ✅ Geração integrada + validação específica para wrappers")
    
    logger.info("🎯 VANTAGENS DA NOVA ABORDAGEM:")
    logger.info("   - Menos chamadas à IA (1 vs 3)")
    logger.info("   - Maior consistência entre arquivos")
    logger.info("   - Validação específica para wrappers")
    logger.info("   - Melhor para componentes simples como wrappers")

def show_usage_examples():
    """
    Mostra exemplos de uso da nova funcionalidade.
    """
    logger.info("📚 EXEMPLOS DE USO DA NOVA FUNCIONALIDADE")
    
    logger.info("\n1️⃣ USO BÁSICO:")
    logger.info("```python")
    logger.info("from src.generators.wrapper_generator import WrapperGenerator")
    logger.info("")
    logger.info("# Configurar gerador")
    logger.info("wrapper_gen = WrapperGenerator('project_config.yaml', flow_client)")
    logger.info("")
    logger.info("# Usar nova geração integrada")
    logger.info("wrapper_gen.generate_wrappers_integrated_from_metadata(output_dir)")
    logger.info("```")
    
    logger.info("\n2️⃣ COMPARAÇÃO COM VERSÃO ANTIGA:")
    logger.info("```python")
    logger.info("# ANTIGA (mantida para compatibilidade)")
    logger.info("wrapper_gen.generate_wrappers_from_metadata(output_dir)")
    logger.info("")
    logger.info("# NOVA (recomendada para wrappers)")
    logger.info("wrapper_gen.generate_wrappers_integrated_from_metadata(output_dir)")
    logger.info("```")
    
    logger.info("\n3️⃣ ESTRUTURA DOS PROMPTS:")
    logger.info("- system_prompts.wrapper_integrated_generation")
    logger.info("- user_prompts.wrapper_integrated_generation")
    logger.info("- validation.wrapper_validation")

if __name__ == "__main__":
    logger.info("🧪 TESTE DA GERAÇÃO INTEGRADA DE WRAPPERS")
    logger.info("=" * 60)
    
    # Mostrar exemplos de uso
    show_usage_examples()
    
    # Comparar abordagens
    compare_old_vs_new_generation()
    
    # Testar funcionalidade (se possível)
    test_wrapper_integrated_generation()
    
    logger.info("=" * 60)
    logger.info("✅ Script de teste concluído!")
