# -*- coding: utf-8 -*-
"""
Wrapper Generator - Geração de componentes wrapper via IA.

Este módulo é responsável pela geração de componentes wrapper Angular
usando IA com modelo de analysis, baseado nos metadados salvos.
"""

import json
import glob
from pathlib import Path
from typing import Dict, Any, List, Tuple

from src.utils.logging import get_logger
from src.utils.file_operations import FileOperations
from src.utils.component_analyzer import ComponentAnalyzer
from src.utils.angular_utils import generate_angular_component_files
from src.generators.utils.generator_config_loader import GeneratorConfigLoader

logger = get_logger(__name__)

class WrapperGenerator:
    """
    Gera componentes wrapper Angular via IA usando modelo de analysis.
    """
    
    def __init__(self, config_path: str = "project_config.yaml", flow_client=None):
        # Usar o novo utilitário de configuração
        self.config_helper = GeneratorConfigLoader(config_path)
        self.config = self.config_helper.get_config()
        self.ai_config = self.config_helper.get_ai_config()
        self.prompts = self.config_helper.load_prompts()
        self.flow_client = flow_client
        

    def generate_wrappers_from_metadata(self, output_dir: str):
        """
        Gera wrappers a partir dos metadados salvos.
        
        Args:
            output_dir: Diretório onde procurar metadados de wrapper
        """
        if not self.flow_client:
            logger.error("❌ Flow API não disponível para geração de wrappers.")
            return
        
        logger.debug(f"🔍 Procurando metadados de wrapper em: {output_dir}")
        
        # Procurar por arquivos de metadados de wrapper
        metadata_files = self._find_metadata_files(output_dir)
        
        if not metadata_files:
            logger.info("ℹ️ Nenhum metadado de wrapper encontrado.")
            return
        
        logger.debug(f"📦 Encontrados {len(metadata_files)} metadados de wrapper para geração integrada")

        for metadata_file in metadata_files:
            try:
                self._generate_single_wrapper_integrated(metadata_file)
            except Exception as e:
                logger.error(f"❌ Erro ao gerar wrapper integrado de {metadata_file}: {e}")

    def _find_metadata_files(self, output_dir: str) -> List[str]:
        """
        Encontra arquivos de metadata de wrappers.
        
        Args:
            output_dir: Diretório de saída
            
        Returns:
            Lista de caminhos para arquivos de metadata
        """
        
        # Procurar na pasta figma_processed em vez de angular
        figma_processed_dir = output_dir.replace('/angular/', '/figma_processed/')
        metadata_pattern = f"{figma_processed_dir}/**/*_wrapper_metadata.json"
        
        metadata_files = glob.glob(metadata_pattern, recursive=True)
        
        logger.debug(f"🔍 Encontrados {len(metadata_files)} arquivos de metadata de wrapper")
        for file in metadata_files:
            logger.debug(f"   📄 {file}")
        
        return metadata_files

    # VERSÃO ANTERIOR
    def _generate_single_wrapper(self, metadata_file: str):
        """
        Gera um wrapper específico a partir do arquivo de metadados.
        
        Args:
            metadata_file: Caminho para o arquivo de metadados
        """
        logger.info(f"🚀 Gerando wrapper: {metadata_file}")
        
        # Carregar metadados usando o utilitário
        metadata = FileOperations.read_json_file(metadata_file)
        if not metadata:
            logger.error(f"❌ Erro ao carregar metadados: {metadata_file}")
            return
        
        wrapper_name = metadata['wrapper_name']
        normalized_name = metadata['normalized_name']
        child_components = metadata['child_components']
        output_dir = metadata['output_dir']
        
        logger.info(f"📦 Wrapper: {wrapper_name}")
        logger.info(f"   Componentes filhos: {child_components}")
        
        # Preparar contexto para IA
        context = self._prepare_wrapper_context(metadata, output_dir)
        
        # Gerar código via IA usando modelo de analysis (versão atual - mantida para compatibilidade)
        html = self._generate_wrapper_html_ai(context)
        ts = self._generate_wrapper_typescript_ai(context, html)
        scss = self._generate_wrapper_scss_ai(context, html)

        # Salvar arquivos do wrapper
        component_dir = generate_angular_component_files(normalized_name, html, ts, scss, output_dir)
        
        logger.info(f"✅ Wrapper gerado: {component_dir}")

    def _generate_single_wrapper_integrated(self, metadata_file: str):
        """
        Gera componente wrapper usando geração integrada.

        Args:
            metadata_file: Caminho para o arquivo de metadados
        """
        logger.info(f"🚀 Gerando wrapper integrado: {metadata_file}")

        # Carregar metadados usando o utilitário
        metadata = FileOperations.read_json_file(metadata_file)
        if not metadata:
            logger.error(f"❌ Erro ao carregar metadados: {metadata_file}")
            return

        wrapper_name = metadata['wrapper_name']
        normalized_name = metadata['normalized_name']
        child_components = metadata['child_components']
        output_dir = metadata['output_dir']

        logger.info(f"📦 Wrapper: {wrapper_name}")
        logger.info(f"   Componentes filhos: {child_components}")

        # Preparar contexto para IA
        context = self._prepare_wrapper_context(metadata, output_dir)

        # Gerar código integrado via IA
        html, ts, scss = self._generate_wrapper_integrated_ai(context)

        # Validar wrapper com IA
        from .utils.ai_analysis_helper import AIAnalysisHelper
        ai_helper = AIAnalysisHelper(self.flow_client, self.prompts, self.ai_config)
        html, ts, scss = ai_helper.validate_wrapper_with_ai(html, ts, scss, context)

        # Salvar arquivos do wrapper
        component_dir = generate_angular_component_files(normalized_name, html, ts, scss, output_dir)

        logger.info(f"✅ Wrapper integrado gerado: {component_dir}")

    def _prepare_wrapper_context(self, metadata: Dict[str, Any], output_dir: str) -> Dict[str, Any]:
        """
        Prepara contexto para geração do wrapper via IA.
        
        Args:
            metadata: Metadados do wrapper
            output_dir: Diretório de saída
            
        Returns:
            Contexto para IA
        """
        wrapper_name = metadata['wrapper_name']
        normalized_name = metadata['normalized_name']
        child_components = metadata['child_components']
        figma_data = metadata['figma_data']
        
        # Coletar informações dos componentes filhos gerados
        child_components_info = []
        for child in child_components:
            child_info = self._get_child_component_info(child, output_dir)
            if child_info:
                child_components_info.append(child_info)
        
        # Contexto para IA
        context = {
            'wrapper_name': wrapper_name,
            'normalized_name': normalized_name,
            'child_components': child_components,
            'child_components_info': child_components_info,
            'figma_data': figma_data,
            'total_children': len(child_components)
        }
        
        logger.debug("📋 Contexto preparado:")
        logger.debug(f"   Wrapper: {wrapper_name}")
        logger.debug(f"   Componentes filhos: {len(child_components)}")
        logger.debug(f"   Informações coletadas: {len(child_components_info)}")
        
        return context

    def _get_child_component_info(self, child_name: str, output_dir: str) -> Dict[str, Any]:
        """
        Coleta informações de um componente filho gerado.

        Args:
            child_name: Nome do componente filho
            output_dir: Diretório de saída

        Returns:
            Informações do componente filho ou dict vazio se não encontrado
        """
        child_dir = Path(output_dir) / child_name

        if not child_dir.exists():
            logger.warning(f"⚠️ Diretório do componente filho não encontrado: {child_dir}")
            return {}
        
        # Procurar por arquivos do componente
        html_file = child_dir / f"{child_name}.component.html"
        ts_file = child_dir / f"{child_name}.component.ts"
        scss_file = child_dir / f"{child_name}.component.scss"
        
        child_info = {
            'name': child_name,
            'path': str(child_dir),
            'has_html': html_file.exists(),
            'has_ts': ts_file.exists(),
            'has_scss': scss_file.exists()
        }
        
        # Ler conteúdo dos arquivos se existirem usando o utilitário
        if html_file.exists():
            child_info['html_content'] = FileOperations.read_text_file(html_file)

        if ts_file.exists():
            child_info['ts_content'] = FileOperations.read_text_file(ts_file)

        if scss_file.exists():
            child_info['scss_content'] = FileOperations.read_text_file(scss_file)
        
        return child_info

    def _generate_wrapper_html_ai(self, context: Dict[str, Any]) -> str:
        """
        Gera HTML do wrapper via IA usando modelo de analysis.
        
        Args:
            context: Contexto para IA
            
        Returns:
            HTML do wrapper
        """
        if not self.flow_client:
            return self._generate_fallback_wrapper_html(context)
        
        logger.info(f"Gerando HTML do wrapper: {context['wrapper_name']}")
        
        # System prompt do YAML
        system_prompt = self.prompts['system_prompts']['wrapper_html_generation']['role'] + "\n" + \
                       self.prompts['system_prompts']['wrapper_html_generation']['mission'] + "\n" + \
                       self.prompts['system_prompts']['wrapper_html_generation']['guidelines']

        # User prompt do YAML
        user_prompt = f"\n\nGere o HTML do componente wrapper Angular '{context['wrapper_name']}' que referencia os seguintes componentes filhos:\n\n"
        user_prompt += f"Componentes filhos:\n{self._format_child_components_for_prompt(context['child_components_info'])}\n\n"
        user_prompt += f"Dados do Figma:\n{json.dumps(context['figma_data'], ensure_ascii=False, indent=2)}"
        user_prompt += self.prompts['user_prompts']['wrapper_html_generation']['task']

        try:
            model = self.ai_config['model']['analysis']  # Usar modelo de analysis
            temperature = 0.1
            max_tokens = 4000  # Menor para analysis
            
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return ComponentAnalyzer.sanitize_ai_response(response)
            
        except Exception as e:
            logger.error(f"❌ Erro na geração de HTML do wrapper: {e}")
            return self._generate_fallback_wrapper_html(context)

    def _generate_wrapper_typescript_ai(self, context: Dict[str, Any], html_content: str) -> str:
        """
        Gera TypeScript do wrapper via IA usando modelo de analysis.
        
        Args:
            context: Contexto para IA
            html_content: Conteúdo HTML gerado do wrapper
        Returns:
            TypeScript do wrapper
        """
        if not self.flow_client:
            return self._generate_fallback_wrapper_typescript(context)
        
        logger.info(f"Gerando TypeScript do wrapper: {context['wrapper_name']}")
        
        # System prompt do YAML
        system_prompt = self.prompts['system_prompts']['wrapper_typescript_generation']['role'] + "\n" + \
                       self.prompts['system_prompts']['wrapper_typescript_generation']['mission'] + "\n" + \
                       self.prompts['system_prompts']['wrapper_typescript_generation']['guidelines']

        # User prompt do YAML
        user_prompt = f"\n\nGere o TypeScript do componente wrapper Angular '{context['wrapper_name']}' que referencia os seguintes componentes filhos:\n\n"
        user_prompt += f"HTML do wrapper:\n{html_content}\n\n"
        user_prompt += f"Componentes filhos:\n{self._format_child_components_for_prompt(context['child_components_info'])}\n\n"
        user_prompt += f"Dados do Figma:\n{json.dumps(context['figma_data'], ensure_ascii=False, indent=2)}"
        user_prompt += self.prompts['user_prompts']['wrapper_typescript_generation']['task']

        try:
            model = self.ai_config['model']['analysis']  # Usar modelo de analysis
            temperature = 0.1
            max_tokens = 4000  # Menor para analysis
            
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return ComponentAnalyzer.sanitize_ai_response(response)
            
        except Exception as e:
            logger.error(f"❌ Erro na geração de TypeScript do wrapper: {e}")
            return self._generate_fallback_wrapper_typescript(context)

    def _generate_wrapper_scss_ai(self, context: Dict[str, Any], html_content: str) -> str:
        """
        Gera SCSS do wrapper via IA usando modelo de analysis.
        
        Args:
            context: Contexto para IA
            html_content: Conteúdo HTML gerado do wrapper
            
        Returns:
            SCSS do wrapper
        """
        if not self.flow_client:
            return self._generate_fallback_wrapper_scss(context)
        
        logger.info(f"Gerando SCSS do wrapper: {context['wrapper_name']}")
        
        # System prompt do YAML
        system_prompt = self.prompts['system_prompts']['wrapper_scss_generation']['role'] + "\n" + \
                       self.prompts['system_prompts']['wrapper_scss_generation']['mission'] + "\n" + \
                       self.prompts['system_prompts']['wrapper_scss_generation']['guidelines']

        # User prompt do YAML
        user_prompt = f"\n\nGere o SCSS do componente wrapper Angular '{context['wrapper_name']}' baseado no HTML gerado e nos dados do Figma:\n\n"
        user_prompt += f"HTML do wrapper:\n{html_content}\n\n"
        user_prompt += f"Dados CSS do Figma:\n{json.dumps(context['figma_data'].get('css', {}), ensure_ascii=False, indent=2)}\n\n"
        user_prompt += self.prompts['user_prompts']['wrapper_scss_generation']['task']

        try:
            model = self.ai_config['model']['analysis']  # Usar modelo de analysis
            temperature = 0.1
            max_tokens = 4000  # Menor para analysis
            
            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return ComponentAnalyzer.sanitize_ai_response(response)
            
        except Exception as e:
            logger.error(f"❌ Erro na geração de SCSS do wrapper: {e}")
            return self._generate_fallback_wrapper_scss(context)

    def _format_child_components_for_prompt(self, child_components_info: List[Dict[str, Any]]) -> str:
        """
        Formata informações dos componentes filhos para o prompt.
        
        Args:
            child_components_info: Lista de informações dos componentes filhos
            
        Returns:
            String formatada para o prompt
        """
        if not child_components_info:
            return "Nenhum componente filho encontrado."
        
        formatted = []
        for i, child in enumerate(child_components_info, 1):
            formatted.append(f"{i}. {child['name']}")
            if child.get('html_content'):
                formatted.append(f"   HTML: {len(child['html_content'])} chars")
            if child.get('ts_content'):
                formatted.append(f"   TS: {len(child['ts_content'])} chars")
            if child.get('scss_content'):
                formatted.append(f"   SCSS: {len(child['scss_content'])} chars")
            formatted.append("")
        
        return "\n".join(formatted)

    def _generate_wrapper_integrated_ai(self, context: Dict[str, Any]) -> Tuple[str, str, str]:
        """
        Gera HTML, TypeScript e SCSS do wrapper em uma única chamada integrada.

        Args:
            context: Contexto para IA

        Returns:
            Tuple[html, typescript, scss]
        """
        if not self.flow_client:
            return self._generate_fallback_wrapper_files(context)

        logger.info(f"🔄 Gerando wrapper integrado: {context['wrapper_name']}")

        # System prompt do YAML
        system_prompt = self.prompts['system_prompts']['wrapper_integrated_generation']['role'] + "\n" + \
                       self.prompts['system_prompts']['wrapper_integrated_generation']['mission'] + "\n" + \
                       self.prompts['system_prompts']['wrapper_integrated_generation']['guidelines']

        # User prompt do YAML formatado com contexto
        user_prompt_template = self.prompts['user_prompts']['wrapper_integrated_generation']['task']

        try:
            # Preparar contexto para formatação
            format_context = {
                'wrapper_name': context['wrapper_name'],
                'normalized_name': context['normalized_name'],
                'child_components': context['child_components'],
                'child_components_info': self._format_child_components_for_prompt(context['child_components_info']),
                'figma_data': json.dumps(context['figma_data'], ensure_ascii=False, indent=2)
            }

            user_prompt = user_prompt_template.format(**format_context)

        except (KeyError, ValueError) as e:
            logger.warning(f"⚠️ Erro na formatação do prompt integrado: {e}")
            user_prompt = user_prompt_template

        try:
            model = self.ai_config['model']['generation']  # Usar modelo de geração para integrado
            temperature = self.ai_config.get('temperature', 0.1)
            max_tokens = self.ai_config.get('max_tokens', 8000)

            response = self.flow_client.with_model(model).get_answer(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=temperature,
                max_tokens=max_tokens
            )

            # Extrair HTML, TypeScript e SCSS da resposta
            html = self._extract_html_from_integrated_response(response)
            typescript = self._extract_typescript_from_integrated_response(response)
            scss = self._extract_scss_from_integrated_response(response)

            logger.info("✅ Geração integrada concluída")
            return html, typescript, scss

        except Exception as e:
            logger.error(f"❌ Erro na geração integrada do wrapper: {e}")
            return self._generate_fallback_wrapper_files(context)

    def _extract_html_from_integrated_response(self, response: str) -> str:
        """Extrai HTML da resposta integrada."""
        import re
        html_match = re.search(r'```html\n(.*?)\n```', response, re.DOTALL)
        if html_match:
            return ComponentAnalyzer.sanitize_ai_response(html_match.group(1).strip())

        # Fallback: procurar HTML sem marcadores
        html_match = re.search(r'<[^>]+>.*</[^>]+>', response, re.DOTALL)
        if html_match:
            return ComponentAnalyzer.sanitize_ai_response(html_match.group(0).strip())

        logger.warning("⚠️ HTML não encontrado na resposta integrada")
        return self._generate_fallback_wrapper_html({})

    def _extract_typescript_from_integrated_response(self, response: str) -> str:
        """Extrai TypeScript da resposta integrada."""
        import re
        ts_match = re.search(r'```typescript\n(.*?)\n```', response, re.DOTALL)
        if ts_match:
            return ComponentAnalyzer.sanitize_ai_response(ts_match.group(1).strip())

        # Fallback: procurar padrão de classe TypeScript
        ts_match = re.search(r'import.*?export class.*?}', response, re.DOTALL)
        if ts_match:
            return ComponentAnalyzer.sanitize_ai_response(ts_match.group(0).strip())

        logger.warning("⚠️ TypeScript não encontrado na resposta integrada")
        return self._generate_fallback_wrapper_typescript({})

    def _extract_scss_from_integrated_response(self, response: str) -> str:
        """Extrai SCSS da resposta integrada."""
        import re
        scss_match = re.search(r'```scss\n(.*?)\n```', response, re.DOTALL)
        if scss_match:
            return ComponentAnalyzer.sanitize_ai_response(scss_match.group(1).strip())

        # Fallback: procurar CSS/SCSS
        scss_match = re.search(r'/\*.*?\*/|[.#][a-zA-Z].*?{.*?}', response, re.DOTALL)
        if scss_match:
            return ComponentAnalyzer.sanitize_ai_response(scss_match.group(0).strip())

        logger.warning("⚠️ SCSS não encontrado na resposta integrada")
        return self._generate_fallback_wrapper_scss({})

    def _generate_fallback_wrapper_files(self, context: Dict[str, Any]) -> Tuple[str, str, str]:
        """Gera arquivos de fallback quando IA não está disponível."""
        html = self._generate_fallback_wrapper_html(context)
        typescript = self._generate_fallback_wrapper_typescript(context)
        scss = self._generate_fallback_wrapper_scss(context)
        return html, typescript, scss

    def _generate_fallback_wrapper_html(self, context: Dict[str, Any]) -> str:
        """Gera HTML de fallback para wrapper."""
        wrapper_name = context['wrapper_name']
        child_components = context['child_components']
        
        html_lines = [f'<!-- Wrapper: {wrapper_name} -->']
        html_lines.append('<div class="wrapper-container">')
        
        for child in child_components:
            html_lines.append(f'  <app-{child}></app-{child}>')
        
        html_lines.append('</div>')
        
        return '\n'.join(html_lines)

    def _generate_fallback_wrapper_typescript(self, context: Dict[str, Any]) -> str:
        """Gera TypeScript de fallback para wrapper."""
        wrapper_name = context['wrapper_name']
        normalized_name = context['normalized_name']
        child_components = context['child_components']
        
        ts_lines = ['import { Component } from \'@angular/core\';']
        
        # Imports dos componentes filhos
        for child in child_components:
            ts_lines.append(f"import {{ {child.title()}Component }} from '../{child}/{child}.component';")
        
        ts_lines.append('')
        ts_lines.append('@Component({')
        ts_lines.append(f"  selector: 'app-{normalized_name}',")
        ts_lines.append(f"  templateUrl: './{normalized_name}.component.html',")
        ts_lines.append(f"  styleUrls: ['./{normalized_name}.component.scss']")
        ts_lines.append('})')
        ts_lines.append('')
        ts_lines.append(f'export class {wrapper_name.replace(" ", "")}Component {{')
        ts_lines.append('  // Wrapper component')
        ts_lines.append('}')
        
        return '\n'.join(ts_lines)

    def _generate_fallback_wrapper_scss(self, context: Dict[str, Any]) -> str:
        """Gera SCSS de fallback para wrapper."""
        wrapper_name = context['wrapper_name']
        
        scss_lines = [f'// Wrapper: {wrapper_name}']
        scss_lines.append('// Erro na geração')
        scss_lines.append('// Implemente o SCSS manualmente')
        
        return '\n'.join(scss_lines) 